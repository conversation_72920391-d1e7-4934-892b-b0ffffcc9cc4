#!/usr/bin/env python3
"""
Simple DXF File Visualizer

A simplified version that focuses on the main geometric entities for better performance
with large DXF files. This version samples entities if there are too many to maintain
reasonable performance.

Author: DXF Visualization Script
Date: 2025-08-20
"""

import ezdxf
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.collections import LineCollection
import numpy as np
import os
import sys
import random


def sample_entities(entities, max_count=1000):
    """Sample entities if there are too many to maintain performance."""
    if len(entities) <= max_count:
        return entities
    return random.sample(entities, max_count)


def extract_and_plot_dxf(dxf_file_path, max_entities_per_type=1000):
    """Extract entities from DXF and create a simplified visualization."""
    
    # Check if file exists
    if not os.path.exists(dxf_file_path):
        print(f"Error: DXF file not found at {dxf_file_path}")
        return None
    
    print(f"Reading DXF file: {dxf_file_path}")
    print(f"File size: {os.path.getsize(dxf_file_path) / (1024*1024):.2f} MB")
    
    try:
        # Read the DXF file
        doc = ezdxf.readfile(dxf_file_path)
        print(f"DXF version: {doc.dxfversion}")
        
        # Get modelspace
        msp = doc.modelspace()
        print(f"Total entities in modelspace: {len(msp)}")
        
        # Create figure
        fig, ax = plt.subplots(1, 1, figsize=(16, 12))
        
        # Track bounds for setting plot limits
        all_x, all_y = [], []
        
        # Extract and plot LINES
        lines = []
        line_entities = list(msp.query('LINE'))
        print(f"Found {len(line_entities)} LINE entities")
        
        # Sample lines if too many
        sampled_lines = sample_entities(line_entities, max_entities_per_type)
        if len(sampled_lines) < len(line_entities):
            print(f"Sampling {len(sampled_lines)} lines out of {len(line_entities)}")
        
        for entity in sampled_lines:
            start = entity.dxf.start
            end = entity.dxf.end
            lines.append([(start.x, start.y), (end.x, end.y)])
            all_x.extend([start.x, end.x])
            all_y.extend([start.y, end.y])
        
        if lines:
            line_collection = LineCollection(lines, colors='blue', linewidths=0.5, alpha=0.7)
            ax.add_collection(line_collection)
        
        # Extract and plot LWPOLYLINES
        polylines = []
        polyline_entities = list(msp.query('LWPOLYLINE'))
        print(f"Found {len(polyline_entities)} LWPOLYLINE entities")
        
        # Sample polylines if too many
        sampled_polylines = sample_entities(polyline_entities, max_entities_per_type // 2)
        if len(sampled_polylines) < len(polyline_entities):
            print(f"Sampling {len(sampled_polylines)} polylines out of {len(polyline_entities)}")
        
        for entity in sampled_polylines:
            points = [(point[0], point[1]) for point in entity.get_points()]
            if len(points) > 1:
                # Convert polyline to line segments
                for i in range(len(points) - 1):
                    polylines.append([points[i], points[i + 1]])
                    all_x.extend([points[i][0], points[i + 1][0]])
                    all_y.extend([points[i][1], points[i + 1][1]])
                
                # Close polyline if it's closed
                if entity.closed and len(points) > 2:
                    polylines.append([points[-1], points[0]])
                    all_x.extend([points[-1][0], points[0][0]])
                    all_y.extend([points[-1][1], points[0][1]])
        
        if polylines:
            polyline_collection = LineCollection(polylines, colors='green', linewidths=0.5, alpha=0.7)
            ax.add_collection(polyline_collection)
        
        # Extract and plot CIRCLES
        circle_entities = list(msp.query('CIRCLE'))
        print(f"Found {len(circle_entities)} CIRCLE entities")
        
        for entity in circle_entities:
            center = entity.dxf.center
            radius = entity.dxf.radius
            circle_patch = patches.Circle(
                (center.x, center.y), 
                radius, 
                fill=False, 
                edgecolor='red', 
                linewidth=0.5,
                alpha=0.7
            )
            ax.add_patch(circle_patch)
            all_x.extend([center.x - radius, center.x + radius])
            all_y.extend([center.y - radius, center.y + radius])
        
        # Extract and plot ARCS
        arc_entities = list(msp.query('ARC'))
        print(f"Found {len(arc_entities)} ARC entities")
        
        for entity in arc_entities:
            center = entity.dxf.center
            radius = entity.dxf.radius
            start_angle = np.radians(entity.dxf.start_angle)
            end_angle = np.radians(entity.dxf.end_angle)
            
            # Handle angle wrapping
            if end_angle < start_angle:
                end_angle += 2 * np.pi
                
            angles = np.linspace(start_angle, end_angle, 50)
            x_points = center.x + radius * np.cos(angles)
            y_points = center.y + radius * np.sin(angles)
            
            ax.plot(x_points, y_points, color='orange', linewidth=0.5, alpha=0.7)
            all_x.extend(x_points)
            all_y.extend(y_points)
        
        # Set equal aspect ratio and adjust limits
        ax.set_aspect('equal')
        
        # Set plot limits with some padding
        if all_x and all_y:
            x_margin = (max(all_x) - min(all_x)) * 0.05
            y_margin = (max(all_y) - min(all_y)) * 0.05
            ax.set_xlim(min(all_x) - x_margin, max(all_x) + x_margin)
            ax.set_ylim(min(all_y) - y_margin, max(all_y) + y_margin)
        
        # Styling
        ax.grid(True, alpha=0.3)
        ax.set_title('Underground Parking Garage Traffic Layout (Simplified View)', 
                    fontsize=16, fontweight='bold')
        ax.set_xlabel('X Coordinate', fontsize=12)
        ax.set_ylabel('Y Coordinate', fontsize=12)
        
        # Add legend with actual counts
        legend_elements = []
        if lines:
            legend_elements.append(plt.Line2D([0], [0], color='blue', lw=2, 
                                            label=f'Lines ({len(lines)} shown)'))
        if polylines:
            legend_elements.append(plt.Line2D([0], [0], color='green', lw=2, 
                                            label=f'Polylines ({len(polylines)} segments)'))
        if circle_entities:
            legend_elements.append(plt.Line2D([0], [0], color='red', lw=2, 
                                            label=f'Circles ({len(circle_entities)})'))
        if arc_entities:
            legend_elements.append(plt.Line2D([0], [0], color='orange', lw=2, 
                                            label=f'Arcs ({len(arc_entities)})'))
        
        if legend_elements:
            ax.legend(handles=legend_elements, loc='upper right')
        
        plt.tight_layout()
        
        # Save the plot
        output_file = 'dxf_simple_visualization.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Simplified visualization saved as: {output_file}")
        
        # Display the plot
        plt.show()
        
        return fig, ax
        
    except Exception as e:
        print(f"Error processing DXF file: {e}")
        return None


def main():
    """Main function."""
    dxf_file_path = '/Users/<USER>/Documents/augment-projects/dxf-test/地下车库交通布局图2clean.dxf'
    
    print("Creating simplified DXF visualization...")
    print("This version samples entities for better performance with large files.")
    print("-" * 60)
    
    result = extract_and_plot_dxf(dxf_file_path, max_entities_per_type=1500)
    
    if result:
        print("-" * 60)
        print("Visualization completed successfully!")
        print("The plot shows the main geometric structure of the DXF file.")
        print("Lines are shown in blue, polylines in green, circles in red, and arcs in orange.")
    else:
        print("Failed to create visualization.")
        sys.exit(1)


if __name__ == "__main__":
    main()
