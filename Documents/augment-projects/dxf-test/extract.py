import ezdxf
import pandas as pd
import os

# --- Configuration ---
DXF_FILE_PATH = './地下车库交通布局图2clean.dxf'
EXCEL_OUTPUT_PATH = "geometry_with_blocks.xlsx"

def extract_geometry(filepath: str, excel_path: str):
    try:
        print(f"--- Loading DXF File: {filepath} ---")
        doc = ezdxf.readfile(filepath)
        msp = doc.modelspace()
        print("File loaded successfully.\n")

        all_data = []

        # --- Collect all LWPOLYLINEs ---
        print("--- Collecting LWPOLYLINEs ---")
        for poly in msp.query("LWPOLYLINE"):
            points = poly.get_points("xy")
            for i, (x, y, *_rest) in enumerate(points):
                all_data.append({
                    "source": "LWPOLYLINE",
                    "handle": poly.dxf.handle,
                    "layer": poly.dxf.layer,
                    "is_closed": poly.is_closed,
                    "vertex_number": i+1,
                    "x": x,
                    "y": y
                })

        # --- Collect block inserts (INSERT) ---
        print("--- Collecting INSERTs (blocks) ---")
        for blockref in msp.query("INSERT"):
            try:
                # virtual_entities applies insertion + rotation + scale
                for e in blockref.virtual_entities():
                    if e.dxftype() in ("LWPOLYLINE", "POLYLINE"):
                        points = e.get_points("xy")
                        for i, (x, y, *_rest) in enumerate(points):
                            all_data.append({
                                "source": f"BLOCK:{blockref.dxf.name}",
                                "handle": blockref.dxf.handle,
                                "layer": blockref.dxf.layer,
                                "is_closed": e.is_closed,
                                "vertex_number": i+1,
                                "x": x,
                                "y": y
                            })
                    elif e.dxftype() == "CIRCLE":
                        # Approximate circle as polygon for Excel export
                        cx, cy = e.dxf.center.x, e.dxf.center.y
                        r = e.dxf.radius
                        for i in range(36):
                            angle = i * 10 * 3.14159/180
                            all_data.append({
                                "source": f"BLOCK:{blockref.dxf.name}",
                                "handle": blockref.dxf.handle,
                                "layer": blockref.dxf.layer,
                                "is_closed": True,
                                "vertex_number": i+1,
                                "x": cx + r * cos(angle),
                                "y": cy + r * sin(angle)
                            })
            except Exception as ex:
                print(f"Skipping block {blockref.dxf.name} due to error: {ex}")

        # Save to Excel
        if all_data:
            df = pd.DataFrame(all_data)
            df.to_excel(excel_path, index=False)
            print(f"✅ Saved {len(all_data)} vertices to {excel_path}")
        else:
            print("No geometry found!")

    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    extract_geometry(DXF_FILE_PATH, EXCEL_OUTPUT_PATH)
