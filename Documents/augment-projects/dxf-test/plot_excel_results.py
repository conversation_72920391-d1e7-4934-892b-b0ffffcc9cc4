#!/usr/bin/env python3
"""
Plot Results from Excel File

This script reads the Excel file generated by extract.py and creates visualizations
of the extracted DXF geometry data.

Author: DXF Visualization Script
Date: 2025-08-20
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.collections import LineCollection
import numpy as np
import os

# Configuration
EXCEL_FILE_PATH = "geometry_with_blocks.xlsx"

def load_and_analyze_excel_data(excel_path):
    """Load Excel data and provide analysis."""
    if not os.path.exists(excel_path):
        print(f"Error: Excel file not found at {excel_path}")
        return None
    
    print(f"Loading data from: {excel_path}")
    df = pd.read_excel(excel_path)
    
    print(f"Total data points: {len(df)}")
    print(f"Columns: {list(df.columns)}")
    
    # Analyze data
    print("\nData Analysis:")
    print("-" * 40)
    
    # Source breakdown
    source_counts = df['source'].value_counts()
    print("Source breakdown:")
    for source, count in source_counts.items():
        print(f"  {source}: {count} points")
    
    # Unique entities
    unique_entities = len(df.groupby(['source', 'handle']))
    print(f"\nUnique entities: {unique_entities}")
    
    # Coordinate ranges
    print(f"\nCoordinate ranges:")
    print(f"  X: {df['x'].min():.2f} to {df['x'].max():.2f}")
    print(f"  Y: {df['y'].min():.2f} to {df['y'].max():.2f}")
    
    return df

def create_comprehensive_plot(df):
    """Create a comprehensive plot of all geometry."""
    print("\nCreating comprehensive plot...")
    
    fig, ax = plt.subplots(1, 1, figsize=(20, 16))
    
    # Group data by source and handle to reconstruct entities
    grouped = df.groupby(['source', 'handle'])
    
    # Separate different types of sources
    lwpolyline_lines = []
    block_lines = []
    
    # Color mapping
    colors = {
        'LWPOLYLINE': 'blue',
        'BLOCK': 'red'
    }
    
    entity_count = 0
    for (source, handle), group in grouped:
        # Sort by vertex number to maintain order
        group_sorted = group.sort_values('vertex_number')
        points = [(row['x'], row['y']) for _, row in group_sorted.iterrows()]
        
        if len(points) > 1:
            # Create line segments
            line_segments = []
            for i in range(len(points) - 1):
                line_segments.append([points[i], points[i + 1]])
            
            # Close if marked as closed
            if group_sorted.iloc[0]['is_closed'] and len(points) > 2:
                line_segments.append([points[-1], points[0]])
            
            # Categorize by source type
            if source == 'LWPOLYLINE':
                lwpolyline_lines.extend(line_segments)
            else:  # Block entities
                block_lines.extend(line_segments)
            
            entity_count += 1
    
    print(f"Processed {entity_count} entities")
    
    # Add line collections
    if lwpolyline_lines:
        lc1 = LineCollection(lwpolyline_lines, colors='blue', linewidths=0.8, alpha=0.7)
        ax.add_collection(lc1)
        print(f"Added {len(lwpolyline_lines)} LWPOLYLINE segments")
    
    if block_lines:
        lc2 = LineCollection(block_lines, colors='red', linewidths=0.8, alpha=0.7)
        ax.add_collection(lc2)
        print(f"Added {len(block_lines)} block entity segments")
    
    # Set equal aspect ratio
    ax.set_aspect('equal')
    
    # Calculate and set bounds
    all_x = df['x'].values
    all_y = df['y'].values
    
    x_margin = (max(all_x) - min(all_x)) * 0.05
    y_margin = (max(all_y) - min(all_y)) * 0.05
    ax.set_xlim(min(all_x) - x_margin, max(all_x) + x_margin)
    ax.set_ylim(min(all_y) - y_margin, max(all_y) + y_margin)
    
    # Styling
    ax.grid(True, alpha=0.3)
    ax.set_title('Underground Parking Garage Layout\n(Extracted from DXF using Block-Aware Method)', 
                fontsize=18, fontweight='bold')
    ax.set_xlabel('X Coordinate', fontsize=14)
    ax.set_ylabel('Y Coordinate', fontsize=14)
    
    # Create legend
    legend_elements = []
    lwpolyline_count = len(df[df['source'] == 'LWPOLYLINE']['handle'].unique())
    block_count = len(df[df['source'].str.startswith('BLOCK')]['handle'].unique())
    
    if lwpolyline_count > 0:
        legend_elements.append(plt.Line2D([0], [0], color='blue', lw=3, 
                                        label=f'Direct LWPOLYLINEs ({lwpolyline_count})'))
    if block_count > 0:
        legend_elements.append(plt.Line2D([0], [0], color='red', lw=3, 
                                        label=f'Block Entities ({block_count})'))
    
    if legend_elements:
        ax.legend(handles=legend_elements, loc='upper right', fontsize=12)
    
    # Add statistics
    stats_text = (f"Total Data Points: {len(df):,}\n"
                 f"Unique Entities: {entity_count:,}\n"
                 f"Coordinate Range: {max(all_x)-min(all_x):.0f} × {max(all_y)-min(all_y):.0f}")
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=11,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.9))
    
    plt.tight_layout()
    return fig, ax

def create_source_breakdown_plot(df):
    """Create a breakdown plot by source type."""
    print("\nCreating source breakdown plot...")
    
    # Analyze sources
    source_stats = df.groupby('source').agg({
        'handle': 'nunique',
        'x': 'count'
    }).rename(columns={'handle': 'unique_entities', 'x': 'total_points'})
    
    # Separate LWPOLYLINE and BLOCK sources
    lwpolyline_stats = source_stats[source_stats.index == 'LWPOLYLINE']
    block_stats = source_stats[source_stats.index.str.startswith('BLOCK')]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Plot 1: Entity counts
    sources = ['Direct\nLWPOLYLINEs', 'Block\nEntities']
    entity_counts = [
        lwpolyline_stats['unique_entities'].sum() if not lwpolyline_stats.empty else 0,
        block_stats['unique_entities'].sum() if not block_stats.empty else 0
    ]
    
    bars1 = ax1.bar(sources, entity_counts, color=['skyblue', 'lightcoral'])
    ax1.set_title('Unique Entities by Source', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Number of Entities', fontsize=12)
    
    # Add value labels
    for bar, count in zip(bars1, entity_counts):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(entity_counts)*0.01,
                str(count), ha='center', va='bottom', fontweight='bold')
    
    # Plot 2: Data point counts
    point_counts = [
        lwpolyline_stats['total_points'].sum() if not lwpolyline_stats.empty else 0,
        block_stats['total_points'].sum() if not block_stats.empty else 0
    ]
    
    bars2 = ax2.bar(sources, point_counts, color=['skyblue', 'lightcoral'])
    ax2.set_title('Total Data Points by Source', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Number of Data Points', fontsize=12)
    
    # Add value labels
    for bar, count in zip(bars2, point_counts):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(point_counts)*0.01,
                f'{count:,}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    return fig, (ax1, ax2)

def create_layer_analysis_plot(df):
    """Create analysis by layer."""
    print("\nCreating layer analysis...")
    
    # Analyze by layer
    layer_stats = df.groupby('layer').agg({
        'handle': 'nunique',
        'x': 'count'
    }).rename(columns={'handle': 'entities', 'x': 'points'}).sort_values('points', ascending=False)
    
    # Take top 10 layers
    top_layers = layer_stats.head(10)
    
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    y_pos = np.arange(len(top_layers))
    bars = ax.barh(y_pos, top_layers['points'], color='lightgreen', alpha=0.7)
    
    ax.set_yticks(y_pos)
    ax.set_yticklabels(top_layers.index)
    ax.set_xlabel('Number of Data Points', fontsize=12)
    ax.set_title('Top 10 Layers by Data Points', fontsize=14, fontweight='bold')
    
    # Add value labels
    for i, (bar, points) in enumerate(zip(bars, top_layers['points'])):
        ax.text(bar.get_width() + max(top_layers['points'])*0.01, bar.get_y() + bar.get_height()/2,
                f'{points:,}', ha='left', va='center')
    
    plt.tight_layout()
    return fig, ax

def main():
    """Main function to create all plots."""
    print("DXF Excel Data Visualization")
    print("=" * 50)
    
    # Load data
    df = load_and_analyze_excel_data(EXCEL_FILE_PATH)
    
    if df is None:
        return
    
    # Create plots
    print("\n" + "=" * 50)
    print("CREATING VISUALIZATIONS")
    print("=" * 50)
    
    # 1. Comprehensive geometry plot
    fig1, ax1 = create_comprehensive_plot(df)
    plt.savefig('excel_geometry_plot.png', dpi=300, bbox_inches='tight')
    print("Saved: excel_geometry_plot.png")
    
    # 2. Source breakdown
    fig2, (ax2a, ax2b) = create_source_breakdown_plot(df)
    plt.savefig('excel_source_breakdown.png', dpi=300, bbox_inches='tight')
    print("Saved: excel_source_breakdown.png")
    
    # 3. Layer analysis
    fig3, ax3 = create_layer_analysis_plot(df)
    plt.savefig('excel_layer_analysis.png', dpi=300, bbox_inches='tight')
    print("Saved: excel_layer_analysis.png")
    
    # Show all plots
    plt.show()
    
    print("\n" + "=" * 50)
    print("VISUALIZATION COMPLETE")
    print("=" * 50)
    print("Generated files:")
    print("- excel_geometry_plot.png: Main geometry visualization")
    print("- excel_source_breakdown.png: Source type analysis")
    print("- excel_layer_analysis.png: Layer breakdown")

if __name__ == "__main__":
    main()
