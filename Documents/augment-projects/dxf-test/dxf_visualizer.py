#!/usr/bin/env python3
"""
DXF File Visualizer with Block Support

This script reads a DXF file, extracts geometric entities including blocks,
and creates a visual plot using matplotlib. It handles various DXF entity types
including lines, polylines, circles, arcs, text, and block inserts.

Author: DXF Visualization Script
Date: 2025-08-20
"""

import ezdxf
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.collections import LineCollection
import numpy as np
import pandas as pd
import os
import sys
from math import cos, sin, pi


def extract_line_entities(msp):
    """Extract LINE entities and return as line segments."""
    lines = []
    for entity in msp.query('LINE'):
        start = entity.dxf.start
        end = entity.dxf.end
        lines.append([(start.x, start.y), (end.x, end.y)])
    return lines


def extract_lwpolyline_entities(msp):
    """Extract LWPOLYLINE entities and return as line segments."""
    polylines = []
    for entity in msp.query('LWPOLYLINE'):
        points = [(point[0], point[1]) for point in entity.get_points()]
        if len(points) > 1:
            # Convert polyline to line segments
            for i in range(len(points) - 1):
                polylines.append([points[i], points[i + 1]])
            # Close polyline if it's closed
            if entity.closed and len(points) > 2:
                polylines.append([points[-1], points[0]])
    return polylines


def extract_circle_entities(msp):
    """Extract CIRCLE entities and return as circle patches."""
    circles = []
    for entity in msp.query('CIRCLE'):
        center = entity.dxf.center
        radius = entity.dxf.radius
        circles.append({'center': (center.x, center.y), 'radius': radius})
    return circles


def extract_arc_entities(msp):
    """Extract ARC entities and return as arc patches."""
    arcs = []
    for entity in msp.query('ARC'):
        center = entity.dxf.center
        radius = entity.dxf.radius
        start_angle = entity.dxf.start_angle
        end_angle = entity.dxf.end_angle
        arcs.append({
            'center': (center.x, center.y),
            'radius': radius,
            'start_angle': start_angle,
            'end_angle': end_angle
        })
    return arcs


def extract_text_entities(msp):
    """Extract TEXT entities and return text information."""
    texts = []
    for entity in msp.query('TEXT'):
        insert_point = entity.dxf.insert
        text_content = entity.dxf.text
        height = entity.dxf.height
        texts.append({
            'position': (insert_point.x, insert_point.y),
            'text': text_content,
            'height': height
        })
    return texts


def plot_dxf_entities(lines, polylines, circles, arcs, texts, title="DXF Visualization"):
    """Create a matplotlib plot of the DXF entities."""
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    
    # Plot lines
    if lines:
        line_collection = LineCollection(lines, colors='blue', linewidths=0.5, alpha=0.7)
        ax.add_collection(line_collection)
    
    # Plot polylines
    if polylines:
        polyline_collection = LineCollection(polylines, colors='green', linewidths=0.5, alpha=0.7)
        ax.add_collection(polyline_collection)
    
    # Plot circles
    for circle in circles:
        circle_patch = patches.Circle(
            circle['center'], 
            circle['radius'], 
            fill=False, 
            edgecolor='red', 
            linewidth=0.5,
            alpha=0.7
        )
        ax.add_patch(circle_patch)
    
    # Plot arcs
    for arc in arcs:
        # Convert arc to a series of points for plotting
        theta1 = np.radians(arc['start_angle'])
        theta2 = np.radians(arc['end_angle'])
        
        # Handle angle wrapping
        if theta2 < theta1:
            theta2 += 2 * np.pi
            
        angles = np.linspace(theta1, theta2, 50)
        x_points = arc['center'][0] + arc['radius'] * np.cos(angles)
        y_points = arc['center'][1] + arc['radius'] * np.sin(angles)
        
        ax.plot(x_points, y_points, color='orange', linewidth=0.5, alpha=0.7)
    
    # Plot text (only if not too many to avoid clutter)
    if len(texts) < 100:  # Limit text display to avoid overcrowding
        for text in texts[:50]:  # Show only first 50 text entities
            ax.text(
                text['position'][0], 
                text['position'][1], 
                text['text'], 
                fontsize=max(1, min(8, text['height'] * 0.1)),
                alpha=0.6,
                color='purple'
            )
    
    # Set equal aspect ratio and adjust limits
    ax.set_aspect('equal')
    
    # Calculate bounds from all entities
    all_x = []
    all_y = []
    
    # Collect coordinates from lines and polylines
    for line_list in [lines, polylines]:
        for line in line_list:
            for point in line:
                all_x.append(point[0])
                all_y.append(point[1])
    
    # Collect coordinates from circles
    for circle in circles:
        all_x.extend([circle['center'][0] - circle['radius'], 
                     circle['center'][0] + circle['radius']])
        all_y.extend([circle['center'][1] - circle['radius'], 
                     circle['center'][1] + circle['radius']])
    
    # Set plot limits with some padding
    if all_x and all_y:
        x_margin = (max(all_x) - min(all_x)) * 0.05
        y_margin = (max(all_y) - min(all_y)) * 0.05
        ax.set_xlim(min(all_x) - x_margin, max(all_x) + x_margin)
        ax.set_ylim(min(all_y) - y_margin, max(all_y) + y_margin)
    
    # Styling
    ax.grid(True, alpha=0.3)
    ax.set_title(title, fontsize=16, fontweight='bold')
    ax.set_xlabel('X Coordinate', fontsize=12)
    ax.set_ylabel('Y Coordinate', fontsize=12)
    
    # Add legend
    legend_elements = []
    if lines:
        legend_elements.append(plt.Line2D([0], [0], color='blue', lw=2, label=f'Lines ({len(lines)})'))
    if polylines:
        legend_elements.append(plt.Line2D([0], [0], color='green', lw=2, label=f'Polylines ({len(polylines)})'))
    if circles:
        legend_elements.append(plt.Line2D([0], [0], color='red', lw=2, label=f'Circles ({len(circles)})'))
    if arcs:
        legend_elements.append(plt.Line2D([0], [0], color='orange', lw=2, label=f'Arcs ({len(arcs)})'))
    if texts and len(texts) < 100:
        legend_elements.append(plt.Line2D([0], [0], color='purple', lw=2, label=f'Text ({min(50, len(texts))})'))
    
    if legend_elements:
        ax.legend(handles=legend_elements, loc='upper right')
    
    plt.tight_layout()
    return fig, ax


def extract_all_entities_comprehensive(doc):
    """Extract ALL entities from ALL layouts including blocks."""
    all_lines = []
    all_polylines = []
    all_circles = []
    all_arcs = []
    all_texts = []

    print("Extracting from ALL layouts and blocks...")

    # Process all layouts (modelspace and paperspace)
    for layout_name in doc.layout_names():
        layout = doc.layouts.get(layout_name)
        print(f"Processing layout: {layout_name}")

        # Extract direct entities from this layout
        layout_lines = extract_line_entities(layout)
        layout_polylines = extract_lwpolyline_entities(layout)
        layout_circles = extract_circle_entities(layout)
        layout_arcs = extract_arc_entities(layout)
        layout_texts = extract_text_entities(layout)

        all_lines.extend(layout_lines)
        all_polylines.extend(layout_polylines)
        all_circles.extend(layout_circles)
        all_arcs.extend(layout_arcs)
        all_texts.extend(layout_texts)

        print(f"  Direct entities - Lines: {len(layout_lines)}, Polylines: {len(layout_polylines)}, "
              f"Circles: {len(layout_circles)}, Arcs: {len(layout_arcs)}, Texts: {len(layout_texts)}")

        # Extract entities from block inserts in this layout
        block_lines = []
        block_polylines = []
        block_circles = []
        block_arcs = []
        block_texts = []

        for blockref in layout.query("INSERT"):
            try:
                for entity in blockref.virtual_entities():
                    if entity.dxftype() == "LINE":
                        start = entity.dxf.start
                        end = entity.dxf.end
                        block_lines.append([(start.x, start.y), (end.x, end.y)])
                    elif entity.dxftype() in ("LWPOLYLINE", "POLYLINE"):
                        points = [(point[0], point[1]) for point in entity.get_points()]
                        if len(points) > 1:
                            for i in range(len(points) - 1):
                                block_polylines.append([points[i], points[i + 1]])
                            if entity.is_closed and len(points) > 2:
                                block_polylines.append([points[-1], points[0]])
                    elif entity.dxftype() == "CIRCLE":
                        center = entity.dxf.center
                        radius = entity.dxf.radius
                        block_circles.append({'center': (center.x, center.y), 'radius': radius})
                    elif entity.dxftype() == "ARC":
                        center = entity.dxf.center
                        radius = entity.dxf.radius
                        start_angle = entity.dxf.start_angle
                        end_angle = entity.dxf.end_angle
                        block_arcs.append({
                            'center': (center.x, center.y),
                            'radius': radius,
                            'start_angle': start_angle,
                            'end_angle': end_angle
                        })
                    elif entity.dxftype() == "TEXT":
                        insert_point = entity.dxf.insert
                        text_content = entity.dxf.text
                        height = entity.dxf.height
                        block_texts.append({
                            'position': (insert_point.x, insert_point.y),
                            'text': text_content,
                            'height': height
                        })
            except Exception as ex:
                continue

        all_lines.extend(block_lines)
        all_polylines.extend(block_polylines)
        all_circles.extend(block_circles)
        all_arcs.extend(block_arcs)
        all_texts.extend(block_texts)

        print(f"  Block entities - Lines: {len(block_lines)}, Polylines: {len(block_polylines)}, "
              f"Circles: {len(block_circles)}, Arcs: {len(block_arcs)}, Texts: {len(block_texts)}")

    return all_lines, all_polylines, all_circles, all_arcs, all_texts


def main():
    """Main function to read DXF file and create comprehensive visualization."""
    # DXF file path
    dxf_file_path = '/Users/<USER>/Documents/augment-projects/dxf-test/地下车库交通布局图2clean.dxf'

    # Check if file exists
    if not os.path.exists(dxf_file_path):
        print(f"Error: DXF file not found at {dxf_file_path}")
        sys.exit(1)

    print(f"Reading DXF file: {dxf_file_path}")
    print(f"File size: {os.path.getsize(dxf_file_path) / (1024*1024):.2f} MB")

    try:
        # Read the DXF file
        doc = ezdxf.readfile(dxf_file_path)
        print(f"DXF version: {doc.dxfversion}")
        print(f"Available layouts: {list(doc.layout_names())}")

        # Extract ALL entities from ALL layouts and blocks
        lines, polylines, circles, arcs, texts = extract_all_entities_comprehensive(doc)

        print(f"\nTOTAL EXTRACTED (all layouts + blocks):")
        print(f"  Lines: {len(lines)}")
        print(f"  Polylines: {len(polylines)}")
        print(f"  Circles: {len(circles)}")
        print(f"  Arcs: {len(arcs)}")
        print(f"  Text entities: {len(texts)}")

        # Create visualization
        print("\nCreating comprehensive visualization...")
        fig, ax = plot_dxf_entities(
            lines, polylines, circles, arcs, texts,
            title="Underground Parking Garage - ALL Objects (All Layouts + Blocks)"
        )

        # Save the plot
        output_file = 'dxf_complete_visualization.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Complete visualization saved as: {output_file}")

        # Display the plot
        plt.show()

    except Exception as e:
        print(f"Error processing DXF file: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
