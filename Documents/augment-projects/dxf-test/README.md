# DXF File Visualization Project

This project provides Python scripts to read, parse, and visualize DXF (Drawing Exchange Format) files using the `ezdxf` library and `matplotlib` for plotting.

## Project Overview

The project includes tools to:
1. Read and parse DXF files
2. Extract geometric entities (lines, polylines, circles, arcs, text)
3. Create visual plots/visualizations of the DXF content
4. Save visualizations as high-resolution PNG images

## Files in this Project

- `地下车库交通布局图2clean.dxf` - The source DXF file (Underground Parking Garage Traffic Layout)
- `dxf_visualizer.py` - Complete DXF visualization script
- `dxf_simple_visualizer.py` - Simplified version for better performance with large files
- `dxf_visualization.png` - Output from the complete visualizer
- `dxf_simple_visualization.png` - Output from the simplified visualizer
- `README.md` - This documentation file

## Setup Instructions

### 1. Create Conda Environment

```bash
conda create -n dxf-demo python=3.11 -y
conda activate dxf-demo
```

### 2. Install Required Packages

```bash
pip install ezdxf matplotlib
```

## Usage

### Complete Visualization Script

The `dxf_visualizer.py` script provides a comprehensive visualization of all supported DXF entities:

```bash
conda activate dxf-demo
python dxf_visualizer.py
```

**Features:**
- Extracts and visualizes lines, polylines, circles, arcs, and text
- Handles large files with thousands of entities
- Creates detailed plots with legends and entity counts
- Saves high-resolution PNG output

### Simplified Visualization Script

The `dxf_simple_visualizer.py` script is optimized for performance with large files:

```bash
conda activate dxf-demo
python dxf_simple_visualizer.py
```

**Features:**
- Samples entities if there are too many (for better performance)
- Focuses on main geometric entities (lines, polylines, circles, arcs)
- Faster processing for large DXF files
- Still provides comprehensive visualization

## DXF File Information

The included DXF file contains:
- **File Size:** 130.55 MB
- **DXF Version:** AC1032 (AutoCAD 2018)
- **Total Entities:** 4,918
- **Entity Breakdown:**
  - Lines: 2,621
  - Polylines: 183 (converted to 504 line segments)
  - Circles: 49
  - Arcs: 12
  - Text: 765
  - Dimensions: 637
  - Block Inserts: 651

## Visualization Features

### Color Coding
- **Blue:** Lines
- **Green:** Polylines
- **Red:** Circles
- **Orange:** Arcs
- **Purple:** Text (limited display to avoid clutter)

### Output Features
- Equal aspect ratio for accurate geometric representation
- Grid overlay for reference
- Legend showing entity types and counts
- Automatic bounds calculation with padding
- High-resolution PNG output (300 DPI)

## Customization

You can modify the scripts to:
- Change colors for different entity types
- Adjust line widths and transparency
- Modify sampling rates for large files
- Add support for additional DXF entity types
- Change output format or resolution

## Technical Notes

### Performance Considerations
- Large DXF files may take time to process
- The simplified visualizer samples entities to maintain performance
- Text entities are limited in display to avoid overcrowding
- Memory usage scales with the number of entities

### Supported Entity Types
- **LINE:** Straight line segments
- **LWPOLYLINE:** Lightweight polylines (converted to line segments)
- **CIRCLE:** Circular entities
- **ARC:** Arc segments
- **TEXT:** Text entities (limited display)

### Coordinate System
- The visualization preserves the original DXF coordinate system
- Equal aspect ratio ensures accurate geometric representation
- Automatic scaling fits all entities within the plot area

## Troubleshooting

### Common Issues
1. **File not found:** Ensure the DXF file path is correct
2. **Memory errors:** Use the simplified visualizer for very large files
3. **Display issues:** Ensure matplotlib backend supports display
4. **Conda environment:** Make sure the dxf-demo environment is activated

### Dependencies
- Python 3.11+
- ezdxf 1.4.2+
- matplotlib 3.10.5+
- numpy 2.3.2+

## Example Output

The scripts generate PNG files showing:
- Complete geometric layout of the DXF file
- Color-coded entity types
- Professional-quality plots suitable for documentation
- High-resolution output for detailed examination

## License

This project is provided as-is for educational and demonstration purposes.
