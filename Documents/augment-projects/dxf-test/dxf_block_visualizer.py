#!/usr/bin/env python3
"""
DXF Block Visualizer

This script reads a DXF file using the provided approach to extract geometry
from both direct entities and block inserts, then creates a visual plot.

Based on the provided code with plotting functionality added.
"""

import ezdxf
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.collections import LineCollection
import numpy as np
import os
from math import cos, sin, pi

# --- Configuration ---
DXF_FILE_PATH = '/Users/<USER>/Documents/augment-projects/dxf-test/地下车库交通布局图2clean.dxf'
EXCEL_OUTPUT_PATH = "geometry_with_blocks.xlsx"

def extract_geometry(filepath: str, excel_path: str):
    """Extract geometry from DXF file including blocks and save to Excel."""
    try:
        print(f"--- Loading DXF File: {filepath} ---")
        doc = ezdxf.readfile(filepath)
        msp = doc.modelspace()
        print("File loaded successfully.\n")

        all_data = []

        # --- Collect all LWPOLYLINEs ---
        print("--- Collecting LWPOLYLINEs ---")
        lwpolyline_count = 0
        for poly in msp.query("LWPOLYLINE"):
            points = poly.get_points("xy")
            for i, (x, y, *_rest) in enumerate(points):
                all_data.append({
                    "source": "LWPOLYLINE",
                    "handle": poly.dxf.handle,
                    "layer": poly.dxf.layer,
                    "is_closed": poly.is_closed,
                    "vertex_number": i+1,
                    "x": x,
                    "y": y
                })
            lwpolyline_count += 1
        print(f"Processed {lwpolyline_count} LWPOLYLINEs")

        # --- Collect block inserts (INSERT) ---
        print("--- Collecting INSERTs (blocks) ---")
        block_count = 0
        block_entity_count = 0
        for blockref in msp.query("INSERT"):
            try:
                # virtual_entities applies insertion + rotation + scale
                for e in blockref.virtual_entities():
                    if e.dxftype() in ("LWPOLYLINE", "POLYLINE"):
                        points = e.get_points("xy")
                        for i, (x, y, *_rest) in enumerate(points):
                            all_data.append({
                                "source": f"BLOCK:{blockref.dxf.name}",
                                "handle": blockref.dxf.handle,
                                "layer": blockref.dxf.layer,
                                "is_closed": e.is_closed,
                                "vertex_number": i+1,
                                "x": x,
                                "y": y
                            })
                        block_entity_count += 1
                    elif e.dxftype() == "CIRCLE":
                        # Approximate circle as polygon for Excel export
                        cx, cy = e.dxf.center.x, e.dxf.center.y
                        r = e.dxf.radius
                        for i in range(36):
                            angle = i * 10 * pi/180
                            all_data.append({
                                "source": f"BLOCK:{blockref.dxf.name}",
                                "handle": blockref.dxf.handle,
                                "layer": blockref.dxf.layer,
                                "is_closed": True,
                                "vertex_number": i+1,
                                "x": cx + r * cos(angle),
                                "y": cy + r * sin(angle)
                            })
                        block_entity_count += 1
                block_count += 1
            except Exception as ex:
                print(f"Skipping block {blockref.dxf.name} due to error: {ex}")
        
        print(f"Processed {block_count} blocks with {block_entity_count} entities")

        # Save to Excel
        if all_data:
            df = pd.DataFrame(all_data)
            df.to_excel(excel_path, index=False)
            print(f"✅ Saved {len(all_data)} vertices to {excel_path}")
            return df
        else:
            print("No geometry found!")
            return None

    except Exception as e:
        print(f"Error: {e}")
        return None


def plot_geometry_from_dataframe(df, title="DXF Geometry Visualization"):
    """Create a plot from the extracted geometry DataFrame."""
    if df is None or df.empty:
        print("No data to plot!")
        return None
    
    print(f"--- Creating plot from {len(df)} data points ---")
    
    # Create figure
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    
    # Group data by source and handle to reconstruct polylines
    grouped = df.groupby(['source', 'handle'])
    
    # Color mapping for different sources
    color_map = {
        'LWPOLYLINE': 'blue',
        'BLOCK': 'red'
    }
    
    lines = []
    circles = []
    
    for (source, handle), group in grouped:
        # Sort by vertex number to maintain order
        group_sorted = group.sort_values('vertex_number')
        points = [(row['x'], row['y']) for _, row in group_sorted.iterrows()]
        
        if len(points) > 1:
            # Create line segments for polylines
            for i in range(len(points) - 1):
                lines.append([points[i], points[i + 1]])
            
            # Close the polyline if it's marked as closed
            if group_sorted.iloc[0]['is_closed'] and len(points) > 2:
                lines.append([points[-1], points[0]])
    
    # Plot all lines
    if lines:
        # Separate lines by source type for different colors
        lwpolyline_lines = []
        block_lines = []
        
        for (source, handle), group in grouped:
            group_sorted = group.sort_values('vertex_number')
            points = [(row['x'], row['y']) for _, row in group_sorted.iterrows()]
            
            if len(points) > 1:
                line_segments = []
                for i in range(len(points) - 1):
                    line_segments.append([points[i], points[i + 1]])
                
                if group_sorted.iloc[0]['is_closed'] and len(points) > 2:
                    line_segments.append([points[-1], points[0]])
                
                if source == 'LWPOLYLINE':
                    lwpolyline_lines.extend(line_segments)
                else:  # Block entities
                    block_lines.extend(line_segments)
        
        # Add line collections
        if lwpolyline_lines:
            lc1 = LineCollection(lwpolyline_lines, colors='blue', linewidths=0.8, alpha=0.7, label='LWPOLYLINEs')
            ax.add_collection(lc1)
        
        if block_lines:
            lc2 = LineCollection(block_lines, colors='red', linewidths=0.8, alpha=0.7, label='Block Entities')
            ax.add_collection(lc2)
    
    # Set equal aspect ratio
    ax.set_aspect('equal')
    
    # Calculate bounds
    all_x = df['x'].values
    all_y = df['y'].values
    
    if len(all_x) > 0 and len(all_y) > 0:
        x_margin = (max(all_x) - min(all_x)) * 0.05
        y_margin = (max(all_y) - min(all_y)) * 0.05
        ax.set_xlim(min(all_x) - x_margin, max(all_x) + x_margin)
        ax.set_ylim(min(all_y) - y_margin, max(all_y) + y_margin)
    
    # Styling
    ax.grid(True, alpha=0.3)
    ax.set_title(title, fontsize=16, fontweight='bold')
    ax.set_xlabel('X Coordinate', fontsize=12)
    ax.set_ylabel('Y Coordinate', fontsize=12)
    
    # Add legend
    legend_elements = []
    lwpolyline_count = len(df[df['source'] == 'LWPOLYLINE']['handle'].unique())
    block_count = len(df[df['source'].str.startswith('BLOCK')]['handle'].unique())
    
    if lwpolyline_count > 0:
        legend_elements.append(plt.Line2D([0], [0], color='blue', lw=2, 
                                        label=f'LWPOLYLINEs ({lwpolyline_count})'))
    if block_count > 0:
        legend_elements.append(plt.Line2D([0], [0], color='red', lw=2, 
                                        label=f'Block Entities ({block_count})'))
    
    if legend_elements:
        ax.legend(handles=legend_elements, loc='upper right')
    
    # Add statistics text
    stats_text = f"Total vertices: {len(df)}\nUnique entities: {len(df.groupby(['source', 'handle']))}"
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    # Save the plot
    output_file = 'dxf_block_visualization.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Visualization saved as: {output_file}")
    
    # Display the plot
    plt.show()
    
    return fig, ax


def main():
    """Main function to extract geometry and create visualization."""
    print("DXF Block Visualizer")
    print("=" * 50)
    
    # Check if file exists
    if not os.path.exists(DXF_FILE_PATH):
        print(f"Error: DXF file not found at {DXF_FILE_PATH}")
        return
    
    # Extract geometry
    df = extract_geometry(DXF_FILE_PATH, EXCEL_OUTPUT_PATH)
    
    if df is not None:
        print("\n" + "=" * 50)
        print("Creating visualization...")
        
        # Create plot
        result = plot_geometry_from_dataframe(
            df, 
            title="Underground Parking Garage - LWPOLYLINEs and Block Entities"
        )
        
        if result:
            print("Visualization completed successfully!")
            print(f"Excel file saved: {EXCEL_OUTPUT_PATH}")
            print(f"Plot saved: dxf_block_visualization.png")
        else:
            print("Failed to create visualization.")
    else:
        print("Failed to extract geometry from DXF file.")


if __name__ == "__main__":
    main()
